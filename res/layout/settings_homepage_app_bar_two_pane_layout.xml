<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (C) 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/app_bar_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/homepage_app_bar_margin_horizontal_two_pane"
    android:padding="@dimen/homepage_app_bar_padding_two_pane"
    android:orientation="horizontal"
    android:background="@drawable/homepage_app_bar_background">

    <include layout="@layout/search_bar_two_pane_version"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/account_avatar_two_pane_version"
        android:layout_width="@dimen/avatar_length"
        android:layout_height="@dimen/avatar_length"
        android:layout_gravity="center"
        android:contentDescription="@string/search_bar_account_avatar_content_description"/>

</LinearLayout>
