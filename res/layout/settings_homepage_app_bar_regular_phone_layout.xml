<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright (C) 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/colorBackground"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/account_avatar"
        android:layout_width="@dimen/avatar_length"
        android:layout_height="@dimen/avatar_length"
        android:layout_marginTop="@dimen/avatar_margin_top"
        android:layout_marginEnd="@dimen/avatar_margin_end"
        android:layout_gravity="end"
        android:visibility="invisible"
        android:accessibilityTraversalAfter="@id/homepage_title"
        android:contentDescription="@string/search_bar_account_avatar_content_description"/>

    <TextView
        android:id="@+id/homepage_title"
        android:text="@string/settings_label"
        android:visibility="gone"
        style="@style/HomepageTitleText"/>

    <FrameLayout
        android:id="@+id/suggestion_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <include layout="@layout/search_bar"
        android:visibility="gone"/>

</LinearLayout>